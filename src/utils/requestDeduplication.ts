/**
 * 请求去重工具
 * 用于防止短时间内的重复请求
 */

interface RequestConfig {
  url: string
  method?: string
  params?: any
  data?: any
}

interface PendingRequest {
  key: string
  timestamp: number
  promise: Promise<any>
}

class RequestDeduplication {
  private pendingRequests: Map<string, PendingRequest> = new Map()
  private readonly timeout: number = 1000 // 默认1秒内的重复请求会被去重

  constructor(timeout?: number) {
    if (timeout) {
      this.timeout = timeout
    }
  }

  /**
   * 生成请求的唯一标识
   */
  private generateRequestKey(config: RequestConfig): string {
    const { url, method = 'GET', params, data } = config
    const paramsStr = params ? JSON.stringify(params) : ''
    const dataStr = data ? JSON.stringify(data) : ''
    return `${method.toUpperCase()}_${url}_${paramsStr}_${dataStr}`
  }

  /**
   * 检查是否为重复请求
   */
  private isDuplicateRequest(key: string): boolean {
    const pending = this.pendingRequests.get(key)
    if (!pending) return false
    
    const now = Date.now()
    // 如果请求超时，清除记录
    if (now - pending.timestamp > this.timeout) {
      this.pendingRequests.delete(key)
      return false
    }
    
    return true
  }

  /**
   * 添加请求到待处理列表
   */
  private addPendingRequest(key: string, promise: Promise<any>): void {
    this.pendingRequests.set(key, {
      key,
      timestamp: Date.now(),
      promise
    })

    // 请求完成后清除记录
    promise.finally(() => {
      this.pendingRequests.delete(key)
    })
  }

  /**
   * 执行去重请求
   */
  async request<T>(config: RequestConfig, requestFn: () => Promise<T>): Promise<T> {
    const key = this.generateRequestKey(config)
    
    // 检查是否为重复请求
    if (this.isDuplicateRequest(key)) {
      console.log(`检测到重复请求，复用结果: ${key}`)
      const pending = this.pendingRequests.get(key)
      return pending!.promise as Promise<T>
    }

    // 执行新请求
    const promise = requestFn()
    this.addPendingRequest(key, promise)
    
    return promise
  }

  /**
   * 清除所有待处理请求
   */
  clear(): void {
    this.pendingRequests.clear()
  }

  /**
   * 清除指定请求
   */
  clearRequest(config: RequestConfig): void {
    const key = this.generateRequestKey(config)
    this.pendingRequests.delete(key)
  }
}

// 创建全局实例
export const requestDeduplication = new RequestDeduplication()

// 导出类供自定义使用
export { RequestDeduplication }

/**
 * 装饰器版本的请求去重
 */
export function deduplicateRequest(timeout?: number) {
  const deduplication = new RequestDeduplication(timeout)
  
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value
    
    descriptor.value = async function (...args: any[]) {
      const config = {
        url: propertyKey,
        method: 'GET',
        params: args[0] || {}
      }
      
      return deduplication.request(config, () => originalMethod.apply(this, args))
    }
    
    return descriptor
  }
}

/**
 * Vue 3 Composition API 的 hook
 */
export function useRequestDeduplication(timeout?: number) {
  const deduplication = new RequestDeduplication(timeout)
  
  const request = async <T>(
    config: RequestConfig,
    requestFn: () => Promise<T>
  ): Promise<T> => {
    return deduplication.request(config, requestFn)
  }
  
  return {
    request,
    clear: () => deduplication.clear(),
    clearRequest: (config: RequestConfig) => deduplication.clearRequest(config)
  }
}
