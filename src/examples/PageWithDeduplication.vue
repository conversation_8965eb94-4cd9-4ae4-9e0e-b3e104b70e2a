<!--
  示例：使用请求去重的页面组件
  这个示例展示了如何在页面组件中使用新的防重复请求工具
-->
<template>
  <div class="page-container">
    <div class="page-header">
      <h2>示例页面</h2>
      <el-button 
        type="primary" 
        :loading="refreshing" 
        @click="refresh"
      >
        手动刷新
      </el-button>
    </div>
    
    <div class="page-content" v-loading="loading">
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="名称" />
        <el-table-column prop="status" label="状态" />
        <el-table-column prop="createTime" label="创建时间" />
      </el-table>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { usePageRefresh } from '/@/composables/usePageRefresh'
import { useRequestDeduplication } from '/@/utils/requestDeduplication'

// 模拟 API 请求
const mockApiRequest = async () => {
  console.log('发起 API 请求...')
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  return [
    { id: 1, name: '项目A', status: '进行中', createTime: '2024-01-01' },
    { id: 2, name: '项目B', status: '已完成', createTime: '2024-01-02' },
    { id: 3, name: '项目C', status: '待开始', createTime: '2024-01-03' }
  ]
}

// 页面数据
const tableData = ref([])

// 数据加载函数
const loadData = async () => {
  try {
    const data = await mockApiRequest()
    tableData.value = data
    console.log('数据加载完成')
  } catch (error) {
    console.error('数据加载失败:', error)
    throw error
  }
}

// 使用页面刷新 hook
const { loading, refreshing, refresh } = usePageRefresh({
  loadData,
  autoLoad: true,
  showLoading: true,
  deduplicationTimeout: 2000 // 2秒内的重复请求会被去重
})

// 方法二：直接使用请求去重工具
const { request } = useRequestDeduplication(1000)

const manualLoadData = async () => {
  const config = {
    url: '/api/example/data',
    method: 'GET',
    params: { page: 1, size: 10 }
  }
  
  try {
    const data = await request(config, mockApiRequest)
    tableData.value = data
  } catch (error) {
    console.error('手动加载数据失败:', error)
  }
}
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-content {
  min-height: 400px;
}
</style>
