/**
 * 页面刷新相关的组合式函数
 * 用于处理页面刷新时的数据加载和防重复请求
 */

import { ref, onMounted, onUnmounted, getCurrentInstance } from 'vue'
import { useRequestDeduplication } from '/@/utils/requestDeduplication'

interface UsePageRefreshOptions {
  /**
   * 数据加载函数
   */
  loadData: () => Promise<any>
  
  /**
   * 是否在组件挂载时自动加载数据
   */
  autoLoad?: boolean
  
  /**
   * 请求去重超时时间（毫秒）
   */
  deduplicationTimeout?: number
  
  /**
   * 是否显示加载状态
   */
  showLoading?: boolean
}

export function usePageRefresh(options: UsePageRefreshOptions) {
  const {
    loadData,
    autoLoad = true,
    deduplicationTimeout = 1000,
    showLoading = true
  } = options

  const { proxy } = getCurrentInstance() as any
  const { request } = useRequestDeduplication(deduplicationTimeout)
  
  const loading = ref(false)
  const refreshing = ref(false)

  /**
   * 加载数据（带去重保护）
   */
  const load = async () => {
    if (loading.value) {
      console.log('数据正在加载中，跳过重复请求')
      return
    }

    const config = {
      url: 'page-data-load',
      method: 'GET',
      params: { timestamp: Date.now() }
    }

    try {
      if (showLoading) {
        loading.value = true
      }
      
      await request(config, loadData)
    } catch (error) {
      console.error('数据加载失败:', error)
      throw error
    } finally {
      if (showLoading) {
        loading.value = false
      }
    }
  }

  /**
   * 刷新数据
   */
  const refresh = async () => {
    if (refreshing.value) {
      console.log('页面正在刷新中，跳过重复刷新')
      return
    }

    try {
      refreshing.value = true
      await load()
    } finally {
      refreshing.value = false
    }
  }

  /**
   * 监听标签页刷新事件
   */
  const handleTagsViewRefresh = () => {
    console.log('收到标签页刷新事件，开始刷新数据')
    refresh()
  }

  // 组件挂载时的处理
  onMounted(() => {
    // 监听标签页刷新事件
    if (proxy?.mittBus) {
      proxy.mittBus.on('onTagsViewRefreshRouterView', handleTagsViewRefresh)
    }

    // 自动加载数据
    if (autoLoad) {
      load()
    }
  })

  // 组件卸载时的清理
  onUnmounted(() => {
    if (proxy?.mittBus) {
      proxy.mittBus.off('onTagsViewRefreshRouterView', handleTagsViewRefresh)
    }
  })

  return {
    loading,
    refreshing,
    load,
    refresh
  }
}

/**
 * 简化版本的页面刷新 hook
 * 适用于简单的数据加载场景
 */
export function useSimplePageRefresh(loadDataFn: () => Promise<any>) {
  return usePageRefresh({
    loadData: loadDataFn,
    autoLoad: true,
    showLoading: true
  })
}
