<!--
  测试页面：用于验证标签页刷新时的重复请求问题是否已解决
-->
<template>
  <div class="refresh-test-page">
    <div class="page-header">
      <h2>刷新测试页面</h2>
      <p>用于测试标签页刷新功能是否还有重复请求问题</p>
    </div>
    
    <div class="test-info">
      <el-card>
        <template #header>
          <span>请求统计</span>
        </template>
        <div class="stats">
          <div class="stat-item">
            <span class="label">总请求次数:</span>
            <span class="value">{{ requestCount }}</span>
          </div>
          <div class="stat-item">
            <span class="label">组件挂载次数:</span>
            <span class="value">{{ mountCount }}</span>
          </div>
          <div class="stat-item">
            <span class="label">最后请求时间:</span>
            <span class="value">{{ lastRequestTime }}</span>
          </div>
        </div>
      </el-card>
    </div>

    <div class="test-actions">
      <el-card>
        <template #header>
          <span>测试操作</span>
        </template>
        <div class="actions">
          <el-button type="primary" @click="manualRefresh" :loading="loading">
            手动刷新数据
          </el-button>
          <el-button @click="resetStats">
            重置统计
          </el-button>
        </div>
      </el-card>
    </div>

    <div class="test-data">
      <el-card>
        <template #header>
          <span>测试数据</span>
        </template>
        <el-table :data="tableData" v-loading="loading">
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="name" label="名称" />
          <el-table-column prop="timestamp" label="加载时间" />
        </el-table>
      </el-card>
    </div>

    <div class="test-logs">
      <el-card>
        <template #header>
          <span>请求日志</span>
        </template>
        <div class="logs">
          <div v-for="(log, index) in requestLogs" :key="index" class="log-item">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, getCurrentInstance } from 'vue'

// 组件实例
const { proxy } = getCurrentInstance() as any

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const requestCount = ref(0)
const mountCount = ref(0)
const lastRequestTime = ref('')
const requestLogs = ref<Array<{time: string, message: string}>>([])

// 添加日志
const addLog = (message: string) => {
  const time = new Date().toLocaleTimeString()
  requestLogs.value.unshift({ time, message })
  // 只保留最近20条日志
  if (requestLogs.value.length > 20) {
    requestLogs.value = requestLogs.value.slice(0, 20)
  }
}

// 模拟API请求
const mockApiRequest = async () => {
  const requestId = Date.now()
  const message = `发起API请求 #${requestCount.value + 1} (ID: ${requestId})`
  
  addLog(message)
  console.log(message)
  
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 800))
  
  requestCount.value++
  lastRequestTime.value = new Date().toLocaleTimeString()
  
  const completeMessage = `API请求完成 #${requestCount.value} (ID: ${requestId})`
  addLog(completeMessage)
  console.log(completeMessage)
  
  return [
    { id: 1, name: `数据项 1`, timestamp: new Date().toLocaleTimeString() },
    { id: 2, name: `数据项 2`, timestamp: new Date().toLocaleTimeString() },
    { id: 3, name: `数据项 3`, timestamp: new Date().toLocaleTimeString() }
  ]
}

// 加载数据
const loadData = async () => {
  if (loading.value) {
    addLog('数据正在加载中，跳过重复请求')
    console.log('数据正在加载中，跳过重复请求')
    return
  }

  try {
    loading.value = true
    const data = await mockApiRequest()
    tableData.value = data
  } catch (error) {
    console.error('数据加载失败:', error)
    addLog(`数据加载失败: ${error}`)
  } finally {
    loading.value = false
  }
}

// 手动刷新
const manualRefresh = () => {
  addLog('手动触发刷新')
  loadData()
}

// 重置统计
const resetStats = () => {
  requestCount.value = 0
  mountCount.value = 0
  lastRequestTime.value = ''
  requestLogs.value = []
  tableData.value = []
  addLog('统计数据已重置')
}

// 监听标签页刷新事件
const handleTagsViewRefresh = () => {
  addLog('收到标签页刷新事件')
  console.log('收到标签页刷新事件，开始刷新数据')
  loadData()
}

// 组件挂载
onMounted(() => {
  mountCount.value++
  addLog(`组件挂载 #${mountCount.value}`)
  console.log(`组件挂载 #${mountCount.value}`)
  
  // 监听标签页刷新事件
  if (proxy?.mittBus) {
    proxy.mittBus.on('onTagsViewRefreshRouterView', handleTagsViewRefresh)
  }

  // 自动加载数据
  loadData()
})

// 组件卸载
onUnmounted(() => {
  addLog('组件卸载')
  console.log('组件卸载')
  
  if (proxy?.mittBus) {
    proxy.mittBus.off('onTagsViewRefreshRouterView', handleTagsViewRefresh)
  }
})
</script>

<style scoped>
.refresh-test-page {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 10px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
}

.test-info,
.test-actions,
.test-data,
.test-logs {
  margin-bottom: 20px;
}

.stats {
  display: flex;
  gap: 30px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.stat-item .label {
  font-size: 14px;
  color: #909399;
}

.stat-item .value {
  font-size: 18px;
  font-weight: bold;
  color: #409EFF;
}

.actions {
  display: flex;
  gap: 10px;
}

.logs {
  max-height: 300px;
  overflow-y: auto;
}

.log-item {
  display: flex;
  gap: 10px;
  padding: 5px 0;
  border-bottom: 1px solid #f0f0f0;
}

.log-time {
  color: #909399;
  font-size: 12px;
  min-width: 80px;
}

.log-message {
  color: #303133;
  font-size: 14px;
}
</style>
