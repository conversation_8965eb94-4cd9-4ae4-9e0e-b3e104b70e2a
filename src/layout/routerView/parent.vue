<template>
  <div class="h100">
    <router-view v-slot="{ Component }">
      <keep-alive :include="keepAliveNameList">
        <component :is="Component" :key="refreshRouterViewKey" class="w100" />
      </keep-alive>
    </router-view>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  toRefs,
  reactive,
  getCurrentInstance,
  onBeforeMount,
  onUnmounted,
  nextTick,
  watch
} from 'vue'
import { useRoute } from 'vue-router'
import { useStore } from '/@/store/index'

export default defineComponent({
  name: 'layoutParentView',
  setup() {
    const { proxy } = getCurrentInstance() as any
    const route = useRoute()
    const store = useStore()
    const state: any = reactive({
      refreshRouterViewKey: null,
      keepAliveNameList: [],
      keepAliveNameNewList: [],
      forceRefreshComponents: new Set(), // 记录需要强制刷新的组件
      isRefreshing: false // 防止重复刷新的标志
    })
    // 设置主界面切换动画
    const setTransitionName = computed(() => {
      return store.state.themeConfig.themeConfig.animation
    })
    // 获取布局配置信息
    const getThemeConfig = computed(() => {
      return store.state.themeConfig.themeConfig
    })
    // 获取组件缓存列表(name值)
    const getKeepAliveNames = computed(() => {
      return store.state.keepAliveNames.keepAliveNames
    })
    // 页面加载前，处理缓存，页面刷新时路由缓存处理
    onBeforeMount(() => {
      state.keepAliveNameList = getKeepAliveNames.value
      proxy.mittBus.on('onTagsViewRefreshRouterView', (fullPath: string) => {
        // 防止重复刷新
        if (state.isRefreshing) {
          console.log('刷新操作正在进行中，跳过重复请求')
          return
        }

        console.log('开始标签页刷新操作:', fullPath)
        state.isRefreshing = true

        // 先从缓存中移除当前组件
        state.keepAliveNameList = getKeepAliveNames.value.filter(
          (name: string) => route.name !== name
        )

        // 先设置为 null 强制销毁组件
        state.refreshRouterViewKey = null

        nextTick(() => {
          // 使用带时间戳的 key 确保组件完全重新创建
          state.refreshRouterViewKey = `${fullPath}-refresh-${Date.now()}`
          // 恢复缓存列表
          state.keepAliveNameList = getKeepAliveNames.value

          // 延迟重置刷新标志，确保刷新完成
          setTimeout(() => {
            state.isRefreshing = false
            console.log('标签页刷新操作完成')
          }, 150)
        })
      })

      // 监听强制清理缓存事件
      proxy.mittBus.on('onForceCleanCache', (componentName: string) => {
        console.log('强制清理缓存:', componentName)
        // 记录需要强制刷新的组件
        state.forceRefreshComponents.add(componentName)
        // 临时从 include 列表中移除组件
        state.keepAliveNameList = state.keepAliveNameList.filter((name) => name !== componentName)
        // 强制重新渲染
        state.refreshRouterViewKey = null
        nextTick(() => {
          // 恢复 include 列表
          state.keepAliveNameList = getKeepAliveNames.value
          state.refreshRouterViewKey = route.fullPath
        })
      })
    })
    // 页面卸载时
    onUnmounted(() => {
      proxy.mittBus.off('onTagsViewRefreshRouterView')
      proxy.mittBus.off('onForceCleanCache')
    })
    // 监听路由变化，防止 tagsView 多标签时，切换动画消失
    watch(
      () => route.fullPath,
      () => {
        // 如果正在进行刷新操作，跳过 watch 触发的更新，避免冲突
        if (state.isRefreshing) {
          console.log('正在刷新中，跳过路由变化触发的更新')
          return
        }

        // 检查当前路由的组件是否需要强制刷新
        const currentComponentName = route.name
        if (state.forceRefreshComponents.has(currentComponentName)) {
          console.log('检测到需要强制刷新的组件:', currentComponentName)
          // 使用带时间戳的 key 强制重新创建组件
          state.refreshRouterViewKey = `${route.fullPath}-refresh-${Date.now()}`
          // 从强制刷新列表中移除
          state.forceRefreshComponents.delete(currentComponentName)
        } else {
          state.refreshRouterViewKey = route.fullPath
        }
      },
      { immediate: true }
    )
    return {
      getThemeConfig,
      getKeepAliveNames,
      setTransitionName,
      ...toRefs(state)
    }
  }
})
</script>
